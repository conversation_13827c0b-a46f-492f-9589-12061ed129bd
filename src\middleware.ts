import { authMiddleware } from "@clerk/nextjs";

export default authMiddleware({
  // Routes that can be accessed while signed out
  publicRoutes: [
    "/",
    "/search",
    "/discover",
    "/success-stories",
    "/services",
    "/photography",
    "/verification",
    "/astrology",
    "/counseling",
    "/profile-writing",
    "/referral",
    "/mobile-app",
    "/pricing",
    "/help",
    "/api/location(.*)",
    "/api/public(.*)",
  ],
  // Routes that can always be accessed, and have
  // no authentication information
  ignoredRoutes: [
    "/api/webhooks(.*)",
    "/api/health",
  ],
});

export const config = {
  // Protects all routes, including api/trpc.
  // See https://clerk.com/docs/references/nextjs/auth-middleware
  // for more information about configuring your Middleware
  matcher: ["/((?!.+\\.[\\w]+$|_next).*)", "/", "/(api|trpc)(.*)"],
};
