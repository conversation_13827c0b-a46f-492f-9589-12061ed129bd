import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Providers } from "@/components/providers";
import { Toaster } from "@/components/ui/toaster";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Indian Matrimony - Find Your Perfect Life Partner",
  description:
    "India's most trusted matrimony platform. Find your perfect life partner from thousands of verified profiles. Join millions of happy couples who found love through our platform.",
  keywords:
    "matrimony, marriage, wedding, Indian brides, Indian grooms, life partner, shaadi",
  authors: [{ name: "Indian Matrimony Platform" }],
  openGraph: {
    title: "Indian Matrimony - Find Your Perfect Life Partner",
    description:
      "India's most trusted matrimony platform with verified profiles",
    type: "website",
    locale: "en_IN",
  },
  twitter: {
    card: "summary_large_image",
    title: "Indian Matrimony - Find Your Perfect Life Partner",
    description:
      "India's most trusted matrimony platform with verified profiles",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}
