'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  Heart, 
  Eye, 
  MessageCircle, 
  Star, 
  Users, 
  TrendingUp, 
  Calendar,
  Bell,
  Settings,
  Crown
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

export default function DashboardPage() {
  const [stats, setStats] = useState({
    profileViews: { total: 245, thisWeek: 32 },
    interestsSent: { total: 15, pending: 8, accepted: 4, declined: 3 },
    interestsReceived: { total: 28, pending: 12, accepted: 10, declined: 6 },
    messages: { totalConversations: 6, unread: 3 },
    profileCompleteness: 85,
  });

  const [recentActivities, setRecentActivities] = useState([
    {
      id: 1,
      type: 'profile_view',
      message: '<PERSON><PERSON> viewed your profile',
      time: '2 hours ago',
      avatar: '/api/placeholder/40/40',
    },
    {
      id: 2,
      type: 'interest_received',
      message: 'You received an interest from <PERSON>',
      time: '5 hours ago',
      avatar: '/api/placeholder/40/40',
    },
    {
      id: 3,
      type: 'message',
      message: 'New message from Deepika <PERSON>',
      time: '1 day ago',
      avatar: '/api/placeholder/40/40',
    },
    {
      id: 4,
      type: 'interest_accepted',
      message: 'Kavya Reddy accepted your interest',
      time: '2 days ago',
      avatar: '/api/placeholder/40/40',
    },
  ]);

  const [recommendations, setRecommendations] = useState([
    {
      id: 1,
      name: 'Meera Gupta',
      age: 26,
      city: 'Bangalore',
      occupation: 'Software Engineer',
      matchScore: 94,
      photo: '/api/placeholder/150/200',
      isNew: true,
    },
    {
      id: 2,
      name: 'Riya Jain',
      age: 24,
      city: 'Mumbai',
      occupation: 'Doctor',
      matchScore: 91,
      photo: '/api/placeholder/150/200',
      isNew: false,
    },
    {
      id: 3,
      name: 'Sneha Iyer',
      age: 27,
      city: 'Chennai',
      occupation: 'Teacher',
      matchScore: 88,
      photo: '/api/placeholder/150/200',
      isNew: true,
    },
  ]);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'profile_view':
        return <Eye className="h-4 w-4 text-blue-500" />;
      case 'interest_received':
        return <Heart className="h-4 w-4 text-rose-500" />;
      case 'message':
        return <MessageCircle className="h-4 w-4 text-green-500" />;
      case 'interest_accepted':
        return <Star className="h-4 w-4 text-yellow-500" />;
      default:
        return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
              <p className="text-gray-600">Welcome back! Here's your matrimony journey overview.</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
              <Button size="sm" className="bg-rose-600 hover:bg-rose-700">
                <Crown className="h-4 w-4 mr-2" />
                Upgrade to Premium
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Profile Views</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.profileViews.total}</div>
              <p className="text-xs text-muted-foreground">
                +{stats.profileViews.thisWeek} this week
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Interests Sent</CardTitle>
              <Heart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.interestsSent.total}</div>
              <p className="text-xs text-muted-foreground">
                {stats.interestsSent.pending} pending responses
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Interests Received</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.interestsReceived.total}</div>
              <p className="text-xs text-muted-foreground">
                {stats.interestsReceived.pending} awaiting your response
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Conversations</CardTitle>
              <MessageCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.messages.totalConversations}</div>
              <p className="text-xs text-muted-foreground">
                {stats.messages.unread} unread messages
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Profile Completeness */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Profile Completeness
                  <Badge variant="secondary">{stats.profileCompleteness}%</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Progress value={stats.profileCompleteness} className="mb-4" />
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Add more photos</span>
                    <span className="text-rose-600">+10%</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Complete family details</span>
                    <span className="text-rose-600">+5%</span>
                  </div>
                </div>
                <Button size="sm" className="mt-4 bg-rose-600 hover:bg-rose-700">
                  Complete Profile
                </Button>
              </CardContent>
            </Card>

            {/* Recommended Matches */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Recommended for You
                  <Link href="/search">
                    <Button variant="outline" size="sm">View All</Button>
                  </Link>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {recommendations.map((match) => (
                    <div key={match.id} className="relative group">
                      <div className="relative overflow-hidden rounded-lg">
                        <img
                          src={match.photo}
                          alt={match.name}
                          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        {match.isNew && (
                          <Badge className="absolute top-2 left-2 bg-green-500">
                            New
                          </Badge>
                        )}
                        <Badge className="absolute top-2 right-2 bg-rose-500">
                          {match.matchScore}% Match
                        </Badge>
                      </div>
                      <div className="mt-2">
                        <h4 className="font-semibold text-sm">{match.name}</h4>
                        <p className="text-xs text-gray-600">
                          {match.age} years • {match.city}
                        </p>
                        <p className="text-xs text-gray-600">{match.occupation}</p>
                      </div>
                      <div className="mt-2 flex space-x-2">
                        <Button size="sm" className="flex-1 text-xs bg-rose-600 hover:bg-rose-700">
                          Send Interest
                        </Button>
                        <Button size="sm" variant="outline" className="flex-1 text-xs">
                          View Profile
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Link href="/search">
                    <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center">
                      <Users className="h-6 w-6 mb-2" />
                      <span className="text-xs">Search Profiles</span>
                    </Button>
                  </Link>
                  
                  <Link href="/interests">
                    <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center">
                      <Heart className="h-6 w-6 mb-2" />
                      <span className="text-xs">Manage Interests</span>
                    </Button>
                  </Link>
                  
                  <Link href="/messages">
                    <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center">
                      <MessageCircle className="h-6 w-6 mb-2" />
                      <span className="text-xs">Messages</span>
                    </Button>
                  </Link>
                  
                  <Link href="/profile">
                    <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center">
                      <Settings className="h-6 w-6 mb-2" />
                      <span className="text-xs">Edit Profile</span>
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Recent Activity
                  <Bell className="h-4 w-4" />
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivities.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3">
                      <img
                        src={activity.avatar}
                        alt=""
                        className="w-8 h-8 rounded-full"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          {getActivityIcon(activity.type)}
                          <p className="text-sm text-gray-900 truncate">
                            {activity.message}
                          </p>
                        </div>
                        <p className="text-xs text-gray-500">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
                <Button variant="outline" size="sm" className="w-full mt-4">
                  View All Activities
                </Button>
              </CardContent>
            </Card>

            {/* Subscription Status */}
            <Card>
              <CardHeader>
                <CardTitle>Subscription Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <Badge variant="secondary" className="mb-2">Free Plan</Badge>
                  <p className="text-sm text-gray-600 mb-4">
                    Upgrade to Premium for unlimited features
                  </p>
                  <Button className="w-full bg-rose-600 hover:bg-rose-700">
                    <Crown className="h-4 w-4 mr-2" />
                    Upgrade Now
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Tips */}
            <Card>
              <CardHeader>
                <CardTitle>Tips for Success</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-rose-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-sm text-gray-600">
                      Complete your profile to get 3x more responses
                    </p>
                  </div>
                  <div className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-rose-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-sm text-gray-600">
                      Add more photos to increase profile views
                    </p>
                  </div>
                  <div className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-rose-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-sm text-gray-600">
                      Send personalized interest messages
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
