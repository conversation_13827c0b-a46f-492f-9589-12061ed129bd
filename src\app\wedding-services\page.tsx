'use client';

import { useState } from 'react';
import { 
  Camera, 
  MapPin, 
  Users, 
  Utensils, 
  Music, 
  Flower, 
  Car, 
  Gift,
  Star,
  Phone,
  Mail,
  Calendar,
  Heart,
  Filter
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';

export default function WeddingServicesPage() {
  const [selectedCity, setSelectedCity] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [priceRange, setPriceRange] = useState('all');

  const serviceCategories = [
    { id: 'venues', name: 'Venues', icon: MapPin, color: 'bg-blue-100 text-blue-600' },
    { id: 'photography', name: 'Photography', icon: Camera, color: 'bg-purple-100 text-purple-600' },
    { id: 'catering', name: 'Catering', icon: Utensils, color: 'bg-green-100 text-green-600' },
    { id: 'decoration', name: 'Decoration', icon: Flower, color: 'bg-pink-100 text-pink-600' },
    { id: 'music', name: 'Music & DJ', icon: Music, color: 'bg-yellow-100 text-yellow-600' },
    { id: 'transport', name: 'Transport', icon: Car, color: 'bg-indigo-100 text-indigo-600' },
    { id: 'gifts', name: 'Gifts & Cards', icon: Gift, color: 'bg-red-100 text-red-600' },
    { id: 'planning', name: 'Wedding Planning', icon: Calendar, color: 'bg-teal-100 text-teal-600' },
  ];

  const weddingServices = [
    {
      id: 1,
      name: 'Royal Palace Banquet Hall',
      category: 'venues',
      city: 'Mumbai',
      rating: 4.8,
      reviews: 245,
      price: '₹2,50,000 - ₹5,00,000',
      capacity: '500-1000 guests',
      image: '/api/placeholder/300/200',
      features: ['AC Halls', 'Parking', 'Catering Kitchen', 'Bridal Room'],
      description: 'Luxurious banquet hall perfect for grand wedding celebrations',
      contact: { phone: '+91 98765 43210', email: '<EMAIL>' },
      verified: true,
    },
    {
      id: 2,
      name: 'Memories Photography Studio',
      category: 'photography',
      city: 'Delhi',
      rating: 4.9,
      reviews: 189,
      price: '₹75,000 - ₹2,00,000',
      capacity: 'Full day coverage',
      image: '/api/placeholder/300/200',
      features: ['Pre-wedding Shoot', 'Candid Photography', 'Video Coverage', 'Album Design'],
      description: 'Award-winning photographers capturing your special moments',
      contact: { phone: '+91 98765 43211', email: '<EMAIL>' },
      verified: true,
    },
    {
      id: 3,
      name: 'Spice Garden Catering',
      category: 'catering',
      city: 'Bangalore',
      rating: 4.7,
      reviews: 156,
      price: '₹800 - ₹2,500 per plate',
      capacity: '100-2000 guests',
      image: '/api/placeholder/300/200',
      features: ['Multi-cuisine', 'Live Counters', 'Dessert Station', 'Service Staff'],
      description: 'Authentic Indian cuisine with modern presentation',
      contact: { phone: '+91 98765 43212', email: '<EMAIL>' },
      verified: true,
    },
    {
      id: 4,
      name: 'Elegant Decorators',
      category: 'decoration',
      city: 'Pune',
      rating: 4.6,
      reviews: 98,
      price: '₹1,50,000 - ₹8,00,000',
      capacity: 'Complete venue decoration',
      image: '/api/placeholder/300/200',
      features: ['Floral Arrangements', 'Lighting', 'Stage Decoration', 'Entrance Decor'],
      description: 'Creating magical wedding atmospheres with stunning decorations',
      contact: { phone: '+91 98765 43213', email: '<EMAIL>' },
      verified: true,
    },
    {
      id: 5,
      name: 'Rhythm Music & Entertainment',
      category: 'music',
      city: 'Chennai',
      rating: 4.5,
      reviews: 134,
      price: '₹25,000 - ₹1,50,000',
      capacity: 'Full event coverage',
      image: '/api/placeholder/300/200',
      features: ['Live Band', 'DJ Services', 'Sound System', 'Dance Floor'],
      description: 'Professional music and entertainment for unforgettable celebrations',
      contact: { phone: '+91 98765 43214', email: '<EMAIL>' },
      verified: false,
    },
    {
      id: 6,
      name: 'Dream Wedding Planners',
      category: 'planning',
      city: 'Hyderabad',
      rating: 4.9,
      reviews: 87,
      price: '₹2,00,000 - ₹10,00,000',
      capacity: 'Complete wedding management',
      image: '/api/placeholder/300/200',
      features: ['Full Planning', 'Vendor Coordination', 'Timeline Management', 'Guest Management'],
      description: 'End-to-end wedding planning services for stress-free celebrations',
      contact: { phone: '+91 98765 43215', email: '<EMAIL>' },
      verified: true,
    },
  ];

  const cities = ['Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Pune', 'Hyderabad', 'Kolkata', 'Ahmedabad'];

  const filteredServices = weddingServices.filter(service => {
    const cityMatch = !selectedCity || service.city === selectedCity;
    const categoryMatch = selectedCategory === 'all' || service.category === selectedCategory;
    return cityMatch && categoryMatch;
  });

  const renderServiceCard = (service: any) => (
    <Card key={service.id} className="overflow-hidden hover:shadow-lg transition-shadow">
      <div className="relative">
        <img
          src={service.image}
          alt={service.name}
          className="w-full h-48 object-cover"
        />
        {service.verified && (
          <Badge className="absolute top-3 right-3 bg-green-600">
            Verified Partner
          </Badge>
        )}
      </div>
      
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-3">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">{service.name}</h3>
            <div className="flex items-center text-sm text-gray-600">
              <MapPin className="h-4 w-4 mr-1" />
              {service.city}
            </div>
          </div>
          
          <div className="text-right">
            <div className="flex items-center mb-1">
              <Star className="h-4 w-4 text-yellow-500 fill-current" />
              <span className="text-sm font-medium ml-1">{service.rating}</span>
            </div>
            <div className="text-xs text-gray-500">({service.reviews} reviews)</div>
          </div>
        </div>

        <p className="text-gray-600 text-sm mb-4">{service.description}</p>

        <div className="mb-4">
          <div className="flex items-center justify-between text-sm mb-2">
            <span className="text-gray-600">Price Range:</span>
            <span className="font-medium text-green-600">{service.price}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Capacity:</span>
            <span className="font-medium">{service.capacity}</span>
          </div>
        </div>

        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Features:</h4>
          <div className="flex flex-wrap gap-1">
            {service.features.map((feature: string, index: number) => (
              <Badge key={index} variant="outline" className="text-xs">
                {feature}
              </Badge>
            ))}
          </div>
        </div>

        <div className="flex space-x-2">
          <Button className="flex-1 bg-rose-600 hover:bg-rose-700">
            <Heart className="h-4 w-4 mr-2" />
            Get Quote
          </Button>
          <Button variant="outline" className="flex-1">
            <Phone className="h-4 w-4 mr-2" />
            Contact
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-pink-600 to-rose-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <Heart className="h-12 w-12 mx-auto mb-4" />
            <h1 className="text-4xl font-bold mb-4">Wedding Services</h1>
            <p className="text-xl text-pink-100 max-w-2xl mx-auto">
              Find trusted vendors and services to make your dream wedding come true
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Service Categories */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Service Categories</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            {serviceCategories.map((category) => (
              <Card 
                key={category.id} 
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedCategory === category.id ? 'ring-2 ring-rose-500' : ''
                }`}
                onClick={() => setSelectedCategory(category.id)}
              >
                <CardContent className="p-4 text-center">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full mb-3 ${category.color}`}>
                    <category.icon className="h-6 w-6" />
                  </div>
                  <h3 className="text-sm font-medium text-gray-900">{category.name}</h3>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <Filter className="h-5 w-5 text-gray-600" />
              <div className="flex-1 grid grid-cols-1 md:grid-cols-4 gap-4">
                <Select value={selectedCity} onValueChange={setSelectedCity}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select City" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Cities</SelectItem>
                    {cities.map((city) => (
                      <SelectItem key={city} value={city}>{city}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Service Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {serviceCategories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={priceRange} onValueChange={setPriceRange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Price Range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Prices</SelectItem>
                    <SelectItem value="budget">Budget (Under ₹1L)</SelectItem>
                    <SelectItem value="mid">Mid-range (₹1L - ₹5L)</SelectItem>
                    <SelectItem value="premium">Premium (₹5L+)</SelectItem>
                  </SelectContent>
                </Select>

                <Input placeholder="Search services..." />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Services Grid */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              Available Services
              <span className="text-lg font-normal text-gray-600 ml-2">
                ({filteredServices.length} services found)
              </span>
            </h2>
            
            <Select defaultValue="rating">
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="rating">Highest Rated</SelectItem>
                <SelectItem value="price-low">Price: Low to High</SelectItem>
                <SelectItem value="price-high">Price: High to Low</SelectItem>
                <SelectItem value="reviews">Most Reviewed</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredServices.map(renderServiceCard)}
          </div>
        </div>

        {/* Wedding Planning Packages */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-center">Complete Wedding Packages</CardTitle>
            <p className="text-center text-gray-600">
              Let us handle everything for your perfect wedding day
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                {
                  name: 'Essential Package',
                  price: '₹5,00,000',
                  features: ['Venue Booking', 'Basic Decoration', 'Photography', 'Catering (200 guests)'],
                  popular: false,
                },
                {
                  name: 'Premium Package',
                  price: '₹12,00,000',
                  features: ['Luxury Venue', 'Premium Decoration', 'Professional Photography & Video', 'Multi-cuisine Catering (500 guests)', 'Music & Entertainment'],
                  popular: true,
                },
                {
                  name: 'Royal Package',
                  price: '₹25,00,000',
                  features: ['Palace/Resort Venue', 'Designer Decoration', 'Celebrity Photographer', 'Premium Catering (1000 guests)', 'Live Band & DJ', 'Wedding Planner', 'Guest Accommodation'],
                  popular: false,
                },
              ].map((pkg, index) => (
                <Card key={index} className={`relative ${pkg.popular ? 'ring-2 ring-rose-500' : ''}`}>
                  {pkg.popular && (
                    <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-rose-600">
                      Most Popular
                    </Badge>
                  )}
                  <CardContent className="p-6 text-center">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <div className="text-3xl font-bold text-rose-600 mb-4">{pkg.price}</div>
                    <ul className="space-y-2 mb-6">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="text-sm text-gray-600 flex items-center">
                          <Star className="h-4 w-4 text-yellow-500 mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button 
                      className={`w-full ${
                        pkg.popular 
                          ? 'bg-rose-600 hover:bg-rose-700' 
                          : 'bg-gray-600 hover:bg-gray-700'
                      }`}
                    >
                      Get Quote
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Contact Section */}
        <Card>
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Need Help Planning Your Wedding?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Our wedding planning experts are here to help you create the perfect celebration. 
              Get personalized recommendations and exclusive deals from our verified partners.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-rose-600 hover:bg-rose-700">
                <Phone className="h-5 w-5 mr-2" />
                Call Wedding Expert
              </Button>
              <Button size="lg" variant="outline">
                <Mail className="h-5 w-5 mr-2" />
                Get Free Consultation
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
