-- Matrimony Platform Database Schema

-- Users table for authentication and basic info
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    is_verified B<PERSON>OLEAN DEFAULT FALSE,
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active' -- active, suspended, deleted
);

-- User profiles with detailed information
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    profile_for VARCHAR(20) NOT NULL, -- self, son, daughter, brother, sister
    
    -- Basic Information
    first_name VA<PERSON>HAR(100) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    gender VARCHAR(10) NOT NULL, -- male, female, other
    date_of_birth DATE NOT NULL,
    marital_status VARCHAR(20) NOT NULL, -- never_married, divorced, widowed, separated
    
    -- Physical Attributes
    height_cm INTEGER,
    weight_kg INTEGER,
    body_type VARCHAR(20), -- slim, average, athletic, heavy
    complexion VARCHAR(20), -- very_fair, fair, wheatish, dark, very_dark
    physical_status VARCHAR(20), -- normal, physically_challenged
    
    -- Location
    country VARCHAR(100) DEFAULT 'India',
    state VARCHAR(100),
    city VARCHAR(100),
    willing_to_relocate BOOLEAN DEFAULT FALSE,
    
    -- Religious Information
    religion VARCHAR(50),
    caste VARCHAR(100),
    sub_caste VARCHAR(100),
    gothra VARCHAR(100),
    star VARCHAR(50), -- nakshatra
    rashi VARCHAR(50),
    manglik_status VARCHAR(20), -- yes, no, anshik
    
    -- Languages
    mother_tongue VARCHAR(50),
    languages_known TEXT[], -- array of languages
    
    -- About sections
    about_me TEXT,
    partner_expectations TEXT,
    
    -- Profile settings
    profile_photo_url VARCHAR(500),
    photo_gallery TEXT[], -- array of photo URLs
    profile_visibility VARCHAR(20) DEFAULT 'public', -- public, private, premium_only
    show_contact_info BOOLEAN DEFAULT FALSE,
    
    -- Verification status
    is_verified BOOLEAN DEFAULT FALSE,
    verification_documents TEXT[], -- array of document URLs
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Family Information
CREATE TABLE family_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    
    family_type VARCHAR(20), -- nuclear, joint, others
    family_status VARCHAR(30), -- middle_class, upper_middle_class, rich, affluent
    family_values VARCHAR(20), -- orthodox, traditional, moderate, liberal
    
    -- Parents Information
    father_name VARCHAR(100),
    father_occupation VARCHAR(100),
    father_living BOOLEAN DEFAULT TRUE,
    mother_name VARCHAR(100),
    mother_occupation VARCHAR(100),
    mother_living BOOLEAN DEFAULT TRUE,
    
    -- Siblings
    brothers_count INTEGER DEFAULT 0,
    brothers_married INTEGER DEFAULT 0,
    sisters_count INTEGER DEFAULT 0,
    sisters_married INTEGER DEFAULT 0,
    
    -- Financial
    family_income_range VARCHAR(30),
    property_details TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Education Details
CREATE TABLE education_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    
    highest_education VARCHAR(100) NOT NULL,
    education_field VARCHAR(100),
    institution_name VARCHAR(200),
    graduation_year INTEGER,
    additional_qualifications TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Professional Details
CREATE TABLE professional_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    
    occupation VARCHAR(100) NOT NULL,
    designation VARCHAR(100),
    company_name VARCHAR(200),
    work_location VARCHAR(100),
    experience_years INTEGER,
    annual_income_range VARCHAR(30),
    willing_to_relocate_for_work BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Lifestyle Preferences
CREATE TABLE lifestyle_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    
    diet VARCHAR(20), -- vegetarian, non_vegetarian, vegan, jain
    smoking VARCHAR(20), -- never, occasionally, regularly, trying_to_quit
    drinking VARCHAR(20), -- never, occasionally, socially, regularly
    
    hobbies TEXT[],
    interests TEXT[],
    music_preferences TEXT[],
    movie_preferences TEXT[],
    book_preferences TEXT[],
    sports_interests TEXT[],
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Partner Preferences
CREATE TABLE partner_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    
    -- Age and Physical
    min_age INTEGER,
    max_age INTEGER,
    min_height_cm INTEGER,
    max_height_cm INTEGER,
    preferred_complexion TEXT[], -- array of acceptable complexions
    preferred_body_type TEXT[],
    
    -- Location
    preferred_countries TEXT[],
    preferred_states TEXT[],
    preferred_cities TEXT[],
    
    -- Religious
    preferred_religions TEXT[],
    preferred_castes TEXT[],
    preferred_sub_castes TEXT[],
    manglik_preference VARCHAR(20), -- yes, no, no_preference
    
    -- Education & Professional
    min_education_level VARCHAR(100),
    preferred_occupations TEXT[],
    min_income_range VARCHAR(30),
    
    -- Lifestyle
    preferred_diet TEXT[],
    smoking_preference VARCHAR(20), -- acceptable, not_acceptable, no_preference
    drinking_preference VARCHAR(20),
    
    -- Family
    preferred_family_type TEXT[],
    preferred_family_values TEXT[],
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Horoscope Details
CREATE TABLE horoscope_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,

    birth_time TIME,
    birth_place VARCHAR(200),
    kundli_url VARCHAR(500), -- URL to uploaded kundli

    -- Astrological details
    sun_sign VARCHAR(20),
    moon_sign VARCHAR(20),
    ascendant VARCHAR(20),
    nakshatra VARCHAR(30),
    pada INTEGER,
    gana VARCHAR(20), -- dev, manushya, rakshasa
    nadi VARCHAR(20),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Interests sent/received between users
CREATE TABLE user_interests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sender_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    receiver_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,

    interest_type VARCHAR(20) DEFAULT 'standard', -- standard, premium, super
    status VARCHAR(20) DEFAULT 'pending', -- pending, accepted, declined, expired
    message TEXT,

    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    responded_at TIMESTAMP,
    expires_at TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL '30 days'),

    UNIQUE(sender_id, receiver_id)
);

-- Messages between matched users
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sender_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    receiver_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,

    message_type VARCHAR(20) DEFAULT 'text', -- text, image, voice, video
    content TEXT,
    media_url VARCHAR(500),

    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Ensure users can only message if they have mutual interest
    CONSTRAINT valid_conversation CHECK (
        EXISTS (
            SELECT 1 FROM user_interests
            WHERE (sender_id = messages.sender_id AND receiver_id = messages.receiver_id AND status = 'accepted')
            OR (sender_id = messages.receiver_id AND receiver_id = messages.sender_id AND status = 'accepted')
        )
    )
);

-- Subscription plans
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    duration_months INTEGER NOT NULL,
    price_inr DECIMAL(10,2) NOT NULL,
    features JSONB, -- JSON object with feature flags
    is_active BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User subscriptions
CREATE TABLE user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    plan_id UUID REFERENCES subscription_plans(id),

    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    payment_status VARCHAR(20) DEFAULT 'pending', -- pending, completed, failed, refunded

    -- Payment details
    payment_id VARCHAR(100),
    payment_method VARCHAR(50),
    amount_paid DECIMAL(10,2),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Profile views tracking
CREATE TABLE profile_views (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    viewer_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    viewed_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,

    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(viewer_id, viewed_profile_id, DATE(viewed_at))
);

-- Shortlisted profiles
CREATE TABLE shortlisted_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    shortlisted_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,

    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(user_id, shortlisted_profile_id)
);

-- Blocked profiles
CREATE TABLE blocked_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    blocked_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,

    reason VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(user_id, blocked_profile_id)
);

-- Reports for inappropriate behavior
CREATE TABLE reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    reporter_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    reported_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,

    report_type VARCHAR(50) NOT NULL, -- fake_profile, inappropriate_message, harassment, etc.
    description TEXT,
    evidence_urls TEXT[], -- screenshots, etc.

    status VARCHAR(20) DEFAULT 'pending', -- pending, investigating, resolved, dismissed
    admin_notes TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP
);

-- Success stories
CREATE TABLE success_stories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user1_id UUID REFERENCES user_profiles(id),
    user2_id UUID REFERENCES user_profiles(id),

    story_title VARCHAR(200),
    story_content TEXT,
    wedding_date DATE,
    wedding_photos TEXT[], -- array of photo URLs

    is_published BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for better performance
CREATE INDEX idx_user_profiles_gender_age ON user_profiles(gender, date_of_birth);
CREATE INDEX idx_user_profiles_location ON user_profiles(country, state, city);
CREATE INDEX idx_user_profiles_religion ON user_profiles(religion, caste);
CREATE INDEX idx_user_interests_status ON user_interests(status, sent_at);
CREATE INDEX idx_messages_conversation ON messages(sender_id, receiver_id, created_at);
CREATE INDEX idx_profile_views_recent ON profile_views(viewed_profile_id, viewed_at);
CREATE INDEX idx_user_subscriptions_active ON user_subscriptions(user_id, is_active, end_date);
