-- Matrimony Platform Database Schema

-- Users table for authentication and basic info
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    is_verified B<PERSON>OLEAN DEFAULT FALSE,
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active' -- active, suspended, deleted
);

-- User profiles with detailed information
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    profile_for VARCHAR(20) NOT NULL, -- self, son, daughter, brother, sister
    
    -- Basic Information
    first_name VA<PERSON>HAR(100) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    gender VARCHAR(10) NOT NULL, -- male, female, other
    date_of_birth DATE NOT NULL,
    marital_status VARCHAR(20) NOT NULL, -- never_married, divorced, widowed, separated
    
    -- Physical Attributes
    height_cm INTEGER,
    weight_kg INTEGER,
    body_type VARCHAR(20), -- slim, average, athletic, heavy
    complexion VARCHAR(20), -- very_fair, fair, wheatish, dark, very_dark
    physical_status VARCHAR(20), -- normal, physically_challenged
    
    -- Location
    country VARCHAR(100) DEFAULT 'India',
    state VARCHAR(100),
    city VARCHAR(100),
    willing_to_relocate BOOLEAN DEFAULT FALSE,
    
    -- Religious Information
    religion VARCHAR(50),
    caste VARCHAR(100),
    sub_caste VARCHAR(100),
    gothra VARCHAR(100),
    star VARCHAR(50), -- nakshatra
    rashi VARCHAR(50),
    manglik_status VARCHAR(20), -- yes, no, anshik
    
    -- Languages
    mother_tongue VARCHAR(50),
    languages_known TEXT[], -- array of languages
    
    -- About sections
    about_me TEXT,
    partner_expectations TEXT,
    
    -- Profile settings
    profile_photo_url VARCHAR(500),
    photo_gallery TEXT[], -- array of photo URLs
    profile_visibility VARCHAR(20) DEFAULT 'public', -- public, private, premium_only
    show_contact_info BOOLEAN DEFAULT FALSE,
    
    -- Verification status
    is_verified BOOLEAN DEFAULT FALSE,
    verification_documents TEXT[], -- array of document URLs
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Family Information
CREATE TABLE family_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    
    family_type VARCHAR(20), -- nuclear, joint, others
    family_status VARCHAR(30), -- middle_class, upper_middle_class, rich, affluent
    family_values VARCHAR(20), -- orthodox, traditional, moderate, liberal
    
    -- Parents Information
    father_name VARCHAR(100),
    father_occupation VARCHAR(100),
    father_living BOOLEAN DEFAULT TRUE,
    mother_name VARCHAR(100),
    mother_occupation VARCHAR(100),
    mother_living BOOLEAN DEFAULT TRUE,
    
    -- Siblings
    brothers_count INTEGER DEFAULT 0,
    brothers_married INTEGER DEFAULT 0,
    sisters_count INTEGER DEFAULT 0,
    sisters_married INTEGER DEFAULT 0,
    
    -- Financial
    family_income_range VARCHAR(30),
    property_details TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Education Details
CREATE TABLE education_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    
    highest_education VARCHAR(100) NOT NULL,
    education_field VARCHAR(100),
    institution_name VARCHAR(200),
    graduation_year INTEGER,
    additional_qualifications TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Professional Details
CREATE TABLE professional_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    
    occupation VARCHAR(100) NOT NULL,
    designation VARCHAR(100),
    company_name VARCHAR(200),
    work_location VARCHAR(100),
    experience_years INTEGER,
    annual_income_range VARCHAR(30),
    willing_to_relocate_for_work BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Lifestyle Preferences
CREATE TABLE lifestyle_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    
    diet VARCHAR(20), -- vegetarian, non_vegetarian, vegan, jain
    smoking VARCHAR(20), -- never, occasionally, regularly, trying_to_quit
    drinking VARCHAR(20), -- never, occasionally, socially, regularly
    
    hobbies TEXT[],
    interests TEXT[],
    music_preferences TEXT[],
    movie_preferences TEXT[],
    book_preferences TEXT[],
    sports_interests TEXT[],
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Partner Preferences
CREATE TABLE partner_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    
    -- Age and Physical
    min_age INTEGER,
    max_age INTEGER,
    min_height_cm INTEGER,
    max_height_cm INTEGER,
    preferred_complexion TEXT[], -- array of acceptable complexions
    preferred_body_type TEXT[],
    
    -- Location
    preferred_countries TEXT[],
    preferred_states TEXT[],
    preferred_cities TEXT[],
    
    -- Religious
    preferred_religions TEXT[],
    preferred_castes TEXT[],
    preferred_sub_castes TEXT[],
    manglik_preference VARCHAR(20), -- yes, no, no_preference
    
    -- Education & Professional
    min_education_level VARCHAR(100),
    preferred_occupations TEXT[],
    min_income_range VARCHAR(30),
    
    -- Lifestyle
    preferred_diet TEXT[],
    smoking_preference VARCHAR(20), -- acceptable, not_acceptable, no_preference
    drinking_preference VARCHAR(20),
    
    -- Family
    preferred_family_type TEXT[],
    preferred_family_values TEXT[],
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Enhanced Horoscope Details with Advanced Astrology
CREATE TABLE horoscope_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,

    -- Birth Details
    birth_time TIME,
    birth_place VARCHAR(200),
    birth_latitude DECIMAL(10, 8),
    birth_longitude DECIMAL(11, 8),
    kundli_url VARCHAR(500), -- URL to uploaded kundli
    kundli_verified BOOLEAN DEFAULT FALSE,

    -- Basic Astrological Details
    sun_sign VARCHAR(20),
    moon_sign VARCHAR(20),
    ascendant VARCHAR(20),
    nakshatra VARCHAR(30),
    pada INTEGER,
    gana VARCHAR(20), -- dev, manushya, rakshasa
    nadi VARCHAR(20),

    -- Advanced Astrological Details
    yoni VARCHAR(30),
    vashya VARCHAR(30),
    varna VARCHAR(20), -- brahmin, kshatriya, vaishya, shudra
    tara INTEGER, -- 1-9
    tithi INTEGER, -- 1-30
    karana VARCHAR(20),
    yoga VARCHAR(30),

    -- Planetary Positions (JSON for flexibility)
    planetary_positions JSONB,

    -- Doshas and Remedies
    manglik_dosha BOOLEAN DEFAULT FALSE,
    manglik_severity VARCHAR(20), -- low, medium, high
    kaal_sarp_dosha BOOLEAN DEFAULT FALSE,
    shani_dosha BOOLEAN DEFAULT FALSE,
    rahu_ketu_dosha BOOLEAN DEFAULT FALSE,
    remedies_suggested TEXT,

    -- Compatibility Scores (calculated)
    guna_milan_score INTEGER, -- out of 36
    compatibility_notes TEXT,

    -- Muhurat and Predictions
    favorable_days TEXT[], -- array of favorable days
    favorable_months TEXT[], -- array of favorable months
    marriage_muhurat_suggestions TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Horoscope Compatibility Results
CREATE TABLE horoscope_compatibility (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    profile1_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    profile2_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,

    -- Guna Milan Scores
    varna_score INTEGER DEFAULT 0, -- out of 1
    vashya_score INTEGER DEFAULT 0, -- out of 2
    tara_score INTEGER DEFAULT 0, -- out of 3
    yoni_score INTEGER DEFAULT 0, -- out of 4
    graha_maitri_score INTEGER DEFAULT 0, -- out of 5
    gana_score INTEGER DEFAULT 0, -- out of 6
    bhakoot_score INTEGER DEFAULT 0, -- out of 7
    nadi_score INTEGER DEFAULT 0, -- out of 8

    total_score INTEGER DEFAULT 0, -- out of 36
    compatibility_percentage DECIMAL(5,2),
    compatibility_level VARCHAR(20), -- excellent, good, average, poor

    -- Detailed Analysis
    strengths TEXT,
    challenges TEXT,
    recommendations TEXT,

    -- Manglik Compatibility
    manglik_compatible BOOLEAN,
    manglik_analysis TEXT,

    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(profile1_id, profile2_id)
);

-- Interests sent/received between users
CREATE TABLE user_interests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sender_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    receiver_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,

    interest_type VARCHAR(20) DEFAULT 'standard', -- standard, premium, super
    status VARCHAR(20) DEFAULT 'pending', -- pending, accepted, declined, expired
    message TEXT,

    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    responded_at TIMESTAMP,
    expires_at TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL '30 days'),

    UNIQUE(sender_id, receiver_id)
);

-- Messages between matched users
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sender_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    receiver_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,

    message_type VARCHAR(20) DEFAULT 'text', -- text, image, voice, video
    content TEXT,
    media_url VARCHAR(500),

    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Ensure users can only message if they have mutual interest
    CONSTRAINT valid_conversation CHECK (
        EXISTS (
            SELECT 1 FROM user_interests
            WHERE (sender_id = messages.sender_id AND receiver_id = messages.receiver_id AND status = 'accepted')
            OR (sender_id = messages.receiver_id AND receiver_id = messages.sender_id AND status = 'accepted')
        )
    )
);

-- Subscription plans
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    duration_months INTEGER NOT NULL,
    price_inr DECIMAL(10,2) NOT NULL,
    features JSONB, -- JSON object with feature flags
    is_active BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User subscriptions
CREATE TABLE user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    plan_id UUID REFERENCES subscription_plans(id),

    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    payment_status VARCHAR(20) DEFAULT 'pending', -- pending, completed, failed, refunded

    -- Payment details
    payment_id VARCHAR(100),
    payment_method VARCHAR(50),
    amount_paid DECIMAL(10,2),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Profile views tracking
CREATE TABLE profile_views (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    viewer_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    viewed_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,

    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(viewer_id, viewed_profile_id, DATE(viewed_at))
);

-- Shortlisted profiles
CREATE TABLE shortlisted_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    shortlisted_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,

    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(user_id, shortlisted_profile_id)
);

-- Blocked profiles
CREATE TABLE blocked_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    blocked_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,

    reason VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(user_id, blocked_profile_id)
);

-- Reports for inappropriate behavior
CREATE TABLE reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    reporter_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    reported_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,

    report_type VARCHAR(50) NOT NULL, -- fake_profile, inappropriate_message, harassment, etc.
    description TEXT,
    evidence_urls TEXT[], -- screenshots, etc.

    status VARCHAR(20) DEFAULT 'pending', -- pending, investigating, resolved, dismissed
    admin_notes TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP
);

-- Success stories
CREATE TABLE success_stories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user1_id UUID REFERENCES user_profiles(id),
    user2_id UUID REFERENCES user_profiles(id),

    story_title VARCHAR(200),
    story_content TEXT,
    wedding_date DATE,
    wedding_photos TEXT[], -- array of photo URLs

    is_published BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Advanced Verification System
CREATE TABLE verification_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    verification_type VARCHAR(50) NOT NULL, -- document, photo, education, professional, background

    -- Document Details
    document_type VARCHAR(50), -- aadhar, passport, driving_license, pan_card, etc.
    document_number VARCHAR(100),
    document_url VARCHAR(500),

    -- Additional Information
    additional_info JSONB,

    -- Verification Status
    status VARCHAR(20) DEFAULT 'pending', -- pending, approved, rejected, requires_resubmission
    verified_by UUID REFERENCES users(id), -- admin who verified
    verification_notes TEXT,
    rejection_reason TEXT,

    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    verified_at TIMESTAMP,

    -- Verification Score (for background checks)
    verification_score INTEGER, -- 0-100
    risk_level VARCHAR(20) -- low, medium, high
);

-- Background Verification Details
CREATE TABLE background_verification (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,

    -- Criminal Background
    criminal_check_status VARCHAR(20), -- clear, pending, issues_found
    criminal_check_details TEXT,

    -- Employment Verification
    employment_verified BOOLEAN DEFAULT FALSE,
    employment_details JSONB,

    -- Education Verification
    education_verified BOOLEAN DEFAULT FALSE,
    education_details JSONB,

    -- Address Verification
    address_verified BOOLEAN DEFAULT FALSE,
    address_details JSONB,

    -- Reference Checks
    references_verified BOOLEAN DEFAULT FALSE,
    reference_details JSONB,

    -- Overall Status
    overall_status VARCHAR(20), -- verified, partial, failed
    verification_score INTEGER, -- 0-100

    -- Verification Agency Details
    agency_name VARCHAR(200),
    agency_report_url VARCHAR(500),

    initiated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);

-- Video Calling Sessions
CREATE TABLE video_calls (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    caller_id UUID REFERENCES users(id) ON DELETE CASCADE,
    callee_id UUID REFERENCES users(id) ON DELETE CASCADE,

    -- Call Details
    call_type VARCHAR(20) DEFAULT 'video', -- video, audio
    status VARCHAR(20) DEFAULT 'initiated', -- initiated, ringing, connected, ended, missed, declined

    -- Session Information
    session_id VARCHAR(200), -- external video service session ID
    room_id VARCHAR(200),

    -- Timing
    initiated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    connected_at TIMESTAMP,
    ended_at TIMESTAMP,
    duration_seconds INTEGER,

    -- Call Quality and Feedback
    call_quality_rating INTEGER, -- 1-5
    caller_feedback TEXT,
    callee_feedback TEXT,

    -- Recording (if enabled and consented)
    recording_url VARCHAR(500),
    recording_consent BOOLEAN DEFAULT FALSE
);

-- Regional and Language Preferences
CREATE TABLE regional_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_profile_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,

    -- Regional Preferences
    preferred_regions TEXT[], -- array of preferred regions/states
    regional_cuisine_preferences TEXT[],
    festival_preferences TEXT[],
    cultural_practices TEXT[],

    -- Language Preferences
    preferred_communication_language VARCHAR(50),
    regional_language_fluency JSONB, -- {"hindi": "fluent", "tamil": "basic"}

    -- Traditional vs Modern Preferences
    lifestyle_preference VARCHAR(20), -- traditional, modern, balanced
    wedding_type_preference VARCHAR(20), -- traditional, modern, destination, simple

    -- Cultural Values
    religious_observance_level VARCHAR(20), -- very_religious, moderately_religious, not_religious
    cultural_openness_level VARCHAR(20), -- very_open, moderately_open, traditional

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Wedding Planning Services
CREATE TABLE wedding_services (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_type VARCHAR(50) NOT NULL, -- venue, catering, photography, decoration, etc.
    service_name VARCHAR(200) NOT NULL,
    service_provider VARCHAR(200),

    -- Location and Availability
    city VARCHAR(100),
    state VARCHAR(100),
    availability_status VARCHAR(20) DEFAULT 'available',

    -- Pricing
    price_range_min DECIMAL(10,2),
    price_range_max DECIMAL(10,2),
    currency VARCHAR(10) DEFAULT 'INR',

    -- Service Details
    description TEXT,
    features TEXT[],
    images TEXT[], -- array of image URLs

    -- Ratings and Reviews
    average_rating DECIMAL(3,2),
    total_reviews INTEGER DEFAULT 0,

    -- Contact Information
    contact_person VARCHAR(100),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(255),
    website_url VARCHAR(500),

    -- Partnership Details
    is_partner BOOLEAN DEFAULT FALSE,
    commission_rate DECIMAL(5,2),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User Wedding Service Bookings
CREATE TABLE wedding_service_bookings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    service_id UUID REFERENCES wedding_services(id) ON DELETE CASCADE,

    -- Booking Details
    booking_status VARCHAR(20) DEFAULT 'inquiry', -- inquiry, confirmed, cancelled, completed
    event_date DATE,
    guest_count INTEGER,

    -- Pricing
    quoted_price DECIMAL(10,2),
    final_price DECIMAL(10,2),
    advance_paid DECIMAL(10,2),

    -- Communication
    special_requirements TEXT,
    booking_notes TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for better performance
CREATE INDEX idx_user_profiles_gender_age ON user_profiles(gender, date_of_birth);
CREATE INDEX idx_user_profiles_location ON user_profiles(country, state, city);
CREATE INDEX idx_user_profiles_religion ON user_profiles(religion, caste);
CREATE INDEX idx_user_interests_status ON user_interests(status, sent_at);
CREATE INDEX idx_messages_conversation ON messages(sender_id, receiver_id, created_at);
CREATE INDEX idx_profile_views_recent ON profile_views(viewed_profile_id, viewed_at);
CREATE INDEX idx_user_subscriptions_active ON user_subscriptions(user_id, is_active, end_date);
CREATE INDEX idx_verification_requests_status ON verification_requests(status, submitted_at);
CREATE INDEX idx_horoscope_compatibility_profiles ON horoscope_compatibility(profile1_id, profile2_id);
CREATE INDEX idx_video_calls_participants ON video_calls(caller_id, callee_id, initiated_at);
CREATE INDEX idx_wedding_services_location ON wedding_services(city, state, service_type);

-- Notifications System
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,

    -- Notification Details
    type VARCHAR(50) NOT NULL, -- interest_received, message_received, profile_viewed, etc.
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,

    -- Related Data
    related_user_id UUID REFERENCES users(id),
    related_profile_id UUID REFERENCES user_profiles(id),
    related_interest_id UUID REFERENCES user_interests(id),
    related_message_id UUID REFERENCES messages(id),

    -- Additional Data (JSON for flexibility)
    data JSONB,

    -- Status
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP,

    -- Delivery
    delivery_method VARCHAR(20) DEFAULT 'in_app', -- in_app, email, sms, push
    delivered_at TIMESTAMP,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Push Notification Tokens
CREATE TABLE push_notification_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,

    device_type VARCHAR(20) NOT NULL, -- ios, android, web
    token VARCHAR(500) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(user_id, token)
);

-- Customer Support System
CREATE TABLE support_tickets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,

    -- Ticket Details
    subject VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(50), -- technical, billing, profile, matching, other
    priority VARCHAR(20) DEFAULT 'medium', -- low, medium, high, urgent
    status VARCHAR(20) DEFAULT 'open', -- open, in_progress, resolved, closed

    -- Assignment
    assigned_to UUID REFERENCES users(id), -- support agent

    -- Resolution
    resolution TEXT,
    satisfaction_rating INTEGER, -- 1-5

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP
);

-- Support Ticket Messages
CREATE TABLE support_ticket_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ticket_id UUID REFERENCES support_tickets(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES users(id) ON DELETE CASCADE,

    message TEXT NOT NULL,
    attachments TEXT[], -- array of file URLs
    is_internal BOOLEAN DEFAULT FALSE, -- internal notes vs customer messages

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Admin Users and Roles
CREATE TABLE admin_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,

    role VARCHAR(50) NOT NULL, -- super_admin, admin, moderator, support_agent
    permissions JSONB, -- detailed permissions

    -- Admin Details
    employee_id VARCHAR(50),
    department VARCHAR(100),

    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(user_id)
);

-- System Analytics
CREATE TABLE analytics_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Event Details
    event_type VARCHAR(100) NOT NULL, -- page_view, profile_view, interest_sent, etc.
    event_category VARCHAR(50), -- user_action, system_event, business_metric

    -- User Context
    user_id UUID REFERENCES users(id),
    session_id VARCHAR(100),

    -- Event Data
    properties JSONB, -- flexible event properties

    -- Technical Details
    ip_address INET,
    user_agent TEXT,
    referrer VARCHAR(500),

    -- Location (if available)
    country VARCHAR(100),
    state VARCHAR(100),
    city VARCHAR(100),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Success Stories Management
CREATE TABLE success_stories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Couple Details
    bride_profile_id UUID REFERENCES user_profiles(id),
    groom_profile_id UUID REFERENCES user_profiles(id),

    -- Story Details
    title VARCHAR(200),
    story_content TEXT,
    wedding_date DATE,

    -- Media
    wedding_photos TEXT[], -- array of photo URLs
    video_url VARCHAR(500),

    -- Publication
    is_published BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    published_at TIMESTAMP,

    -- Engagement
    likes_count INTEGER DEFAULT 0,
    shares_count INTEGER DEFAULT 0,

    -- Consent and Privacy
    consent_given BOOLEAN DEFAULT FALSE,
    privacy_level VARCHAR(20) DEFAULT 'public', -- public, members_only, private

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Mobile App Features
CREATE TABLE app_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    platform VARCHAR(20) NOT NULL, -- ios, android
    version_number VARCHAR(20) NOT NULL,
    build_number INTEGER NOT NULL,

    -- Release Details
    release_notes TEXT,
    is_mandatory_update BOOLEAN DEFAULT FALSE,
    min_supported_version VARCHAR(20),

    -- Feature Flags
    features_enabled JSONB,

    -- Status
    status VARCHAR(20) DEFAULT 'development', -- development, testing, released, deprecated

    released_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User App Usage Analytics
CREATE TABLE app_usage_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,

    -- Session Details
    session_id VARCHAR(100) NOT NULL,
    platform VARCHAR(20), -- ios, android, web
    app_version VARCHAR(20),

    -- Usage Metrics
    session_start TIMESTAMP NOT NULL,
    session_end TIMESTAMP,
    session_duration_seconds INTEGER,

    -- Activity
    screens_visited TEXT[],
    actions_performed JSONB,

    -- Device Info
    device_model VARCHAR(100),
    os_version VARCHAR(50),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Additional Indexes
CREATE INDEX idx_notifications_user_unread ON notifications(user_id, is_read, created_at);
CREATE INDEX idx_support_tickets_status ON support_tickets(status, priority, created_at);
CREATE INDEX idx_analytics_events_type ON analytics_events(event_type, created_at);
CREATE INDEX idx_success_stories_published ON success_stories(is_published, is_featured, published_at);
CREATE INDEX idx_app_usage_user_session ON app_usage_analytics(user_id, session_start);
