import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      name?: string | null
      email?: string | null
      image?: string | null
      isVerified?: boolean
      hasProfile?: boolean
    }
  }

  interface User {
    id: string
    isVerified?: boolean
    hasProfile?: boolean
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string
  }
}
