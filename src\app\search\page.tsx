'use client';

import { useState, useEffect } from 'react';
import { Search, Filter, Heart, Eye, MapPin, Briefcase, GraduationCap, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { SearchFilters } from '@/types/user';
import { calculateAge, formatHeight, getLocationString } from '@/lib/utils';

export default function SearchPage() {
  const [filters, setFilters] = useState<SearchFilters>({
    ageRange: [21, 35],
    heightRange: [150, 180],
  });
  const [showFilters, setShowFilters] = useState(false);
  const [profiles, setProfiles] = useState([]);
  const [loading, setLoading] = useState(false);

  // Mock profile data for demonstration
  const mockProfiles = [
    {
      id: '1',
      firstName: 'Priya',
      lastName: 'Sharma',
      age: 26,
      profilePhotoUrl: '/api/placeholder/300/400',
      city: 'Mumbai',
      state: 'Maharashtra',
      occupation: 'Software Engineer',
      education: 'B.Tech Computer Science',
      heightCm: 165,
      religion: 'Hindu',
      caste: 'Brahmin',
      isVerified: true,
      lastActive: new Date(),
      matchScore: 92,
      isShortlisted: false,
      hasInterest: false,
    },
    {
      id: '2',
      firstName: 'Anita',
      lastName: 'Patel',
      age: 24,
      profilePhotoUrl: '/api/placeholder/300/400',
      city: 'Ahmedabad',
      state: 'Gujarat',
      occupation: 'Doctor',
      education: 'MBBS',
      heightCm: 160,
      religion: 'Hindu',
      caste: 'Patel',
      isVerified: true,
      lastActive: new Date(),
      matchScore: 88,
      isShortlisted: true,
      hasInterest: false,
    },
    {
      id: '3',
      firstName: 'Deepika',
      lastName: 'Singh',
      age: 28,
      profilePhotoUrl: '/api/placeholder/300/400',
      city: 'Delhi',
      state: 'Delhi',
      occupation: 'Marketing Manager',
      education: 'MBA Marketing',
      heightCm: 168,
      religion: 'Hindu',
      caste: 'Rajput',
      isVerified: true,
      lastActive: new Date(),
      matchScore: 85,
      isShortlisted: false,
      hasInterest: true,
      interestStatus: 'sent',
    },
  ];

  useEffect(() => {
    setProfiles(mockProfiles);
  }, []);

  const handleSearch = () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setProfiles(mockProfiles);
      setLoading(false);
    }, 1000);
  };

  const handleSendInterest = (profileId: string) => {
    // Handle sending interest
    console.log('Sending interest to:', profileId);
  };

  const handleShortlist = (profileId: string) => {
    // Handle shortlisting profile
    console.log('Shortlisting profile:', profileId);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Search Profiles</h1>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="lg:hidden"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Filters Sidebar */}
          <div className={`lg:block ${showFilters ? 'block' : 'hidden'}`}>
            <div className="bg-white rounded-lg shadow-sm border p-6 sticky top-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Search Filters</h2>
              
              {/* Quick Search */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quick Search
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by name, ID, or keyword"
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Age Range */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Age Range: {filters.ageRange?.[0]} - {filters.ageRange?.[1]} years
                </label>
                <Slider
                  value={filters.ageRange || [21, 35]}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, ageRange: value as [number, number] }))}
                  max={60}
                  min={18}
                  step={1}
                  className="w-full"
                />
              </div>

              {/* Height Range */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Height Range: {formatHeight(filters.heightRange?.[0] || 150)} - {formatHeight(filters.heightRange?.[1] || 180)}
                </label>
                <Slider
                  value={filters.heightRange || [150, 180]}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, heightRange: value as [number, number] }))}
                  max={200}
                  min={140}
                  step={1}
                  className="w-full"
                />
              </div>

              {/* Location */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Location
                </label>
                <div className="space-y-3">
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Country" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="india">India</SelectItem>
                      <SelectItem value="usa">USA</SelectItem>
                      <SelectItem value="uk">UK</SelectItem>
                      <SelectItem value="canada">Canada</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select State" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="maharashtra">Maharashtra</SelectItem>
                      <SelectItem value="delhi">Delhi</SelectItem>
                      <SelectItem value="karnataka">Karnataka</SelectItem>
                      <SelectItem value="gujarat">Gujarat</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Input placeholder="City" />
                </div>
              </div>

              {/* Religion & Caste */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Religion & Caste
                </label>
                <div className="space-y-3">
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Religion" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hindu">Hindu</SelectItem>
                      <SelectItem value="muslim">Muslim</SelectItem>
                      <SelectItem value="christian">Christian</SelectItem>
                      <SelectItem value="sikh">Sikh</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Caste" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="brahmin">Brahmin</SelectItem>
                      <SelectItem value="kshatriya">Kshatriya</SelectItem>
                      <SelectItem value="vaishya">Vaishya</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Education */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Education
                </label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Education" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="graduate">Graduate</SelectItem>
                    <SelectItem value="postgraduate">Post Graduate</SelectItem>
                    <SelectItem value="doctorate">Doctorate</SelectItem>
                    <SelectItem value="diploma">Diploma</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Profession */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Profession
                </label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Profession" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="engineer">Engineer</SelectItem>
                    <SelectItem value="doctor">Doctor</SelectItem>
                    <SelectItem value="teacher">Teacher</SelectItem>
                    <SelectItem value="business">Business</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Search Button */}
              <Button 
                onClick={handleSearch} 
                className="w-full bg-rose-600 hover:bg-rose-700"
                disabled={loading}
              >
                {loading ? 'Searching...' : 'Search Profiles'}
              </Button>
            </div>
          </div>

          {/* Results */}
          <div className="lg:col-span-3">
            {/* Results Header */}
            <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {profiles.length} profiles found
                  </h3>
                  <p className="text-sm text-gray-600">
                    Showing profiles matching your criteria
                  </p>
                </div>
                
                <Select defaultValue="relevance">
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="relevance">Sort by Relevance</SelectItem>
                    <SelectItem value="newest">Newest First</SelectItem>
                    <SelectItem value="age-low">Age: Low to High</SelectItem>
                    <SelectItem value="age-high">Age: High to Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Profile Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {profiles.map((profile) => (
                <Card key={profile.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="relative">
                    <img
                      src={profile.profilePhotoUrl}
                      alt={`${profile.firstName} ${profile.lastName}`}
                      className="w-full h-64 object-cover"
                    />
                    
                    {/* Verification Badge */}
                    {profile.isVerified && (
                      <div className="absolute top-3 left-3">
                        <Badge className="bg-green-500 text-white">
                          ✓ Verified
                        </Badge>
                      </div>
                    )}
                    
                    {/* Match Score */}
                    <div className="absolute top-3 right-3">
                      <Badge className="bg-rose-500 text-white">
                        {profile.matchScore}% Match
                      </Badge>
                    </div>
                    
                    {/* Quick Actions */}
                    <div className="absolute bottom-3 right-3 flex space-x-2">
                      <Button
                        size="sm"
                        variant="secondary"
                        className="bg-white/90 hover:bg-white"
                        onClick={() => handleShortlist(profile.id)}
                      >
                        <Heart className={`h-4 w-4 ${profile.isShortlisted ? 'fill-rose-500 text-rose-500' : ''}`} />
                      </Button>
                      <Button
                        size="sm"
                        variant="secondary"
                        className="bg-white/90 hover:bg-white"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <CardContent className="p-4">
                    <div className="mb-3">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {profile.firstName} {profile.lastName}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {profile.age} years • {formatHeight(profile.heightCm)}
                      </p>
                    </div>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
                        {getLocationString(profile.city, profile.state)}
                      </div>
                      
                      <div className="flex items-center text-sm text-gray-600">
                        <Briefcase className="h-4 w-4 mr-2 flex-shrink-0" />
                        {profile.occupation}
                      </div>
                      
                      <div className="flex items-center text-sm text-gray-600">
                        <GraduationCap className="h-4 w-4 mr-2 flex-shrink-0" />
                        {profile.education}
                      </div>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        className="flex-1 bg-rose-600 hover:bg-rose-700"
                        onClick={() => handleSendInterest(profile.id)}
                        disabled={profile.hasInterest}
                      >
                        {profile.hasInterest ? 'Interest Sent' : 'Send Interest'}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1"
                      >
                        View Profile
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            
            {/* Load More */}
            <div className="text-center mt-8">
              <Button variant="outline" size="lg">
                Load More Profiles
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
